version: '3.8'

services:
  ai-agent-demo:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PORT=8000
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - OPENWEATHER_API_KEY=${OPENWEATHER_API_KEY:-}
      - ZAPIER_WEBHOOK_URL=${ZAPIER_WEBHOOK_URL:-}
    env_file:
      - ai-agent-demo/.env
    volumes:
      # Mount the .env file if it exists
      - ./ai-agent-demo/.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - ai-agent-network

  # Optional: Redis for session management (if needed in future)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - ai-agent-network

networks:
  ai-agent-network:
    driver: bridge

# volumes:
#   redis_data:
