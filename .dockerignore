# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md

# Environment files (will be mounted separately)
.env
.env.local
.env.example

# Node modules (will be installed in container)
ai-agent-demo/client/node_modules
ai-agent-demo/client/dist

# Python cache
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.so
.coverage
.pytest_cache

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs

# Temporary files
tmp
temp
