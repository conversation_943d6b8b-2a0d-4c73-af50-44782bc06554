const CodeIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
    >
      <g clipPath="url(#clip0_1_162)">
        <path
          d="M9.88733 11.8333C9.71667 11.8333 9.546 11.768 9.416 11.638C9.15533 11.3773 9.15533 10.956 9.416 10.6953L11.138 8.97333C11.2633 8.84733 11.3333 8.68 11.3333 8.50133C11.3333 8.32267 11.264 8.156 11.138 8.03L9.416 6.30733C9.15533 6.04667 9.15533 5.62467 9.416 5.36467C9.67667 5.104 10.098 5.104 10.3587 5.36467L12.0807 7.08667C12.458 7.464 12.6667 7.96667 12.6667 8.50067C12.6667 9.03467 12.4587 9.53733 12.0807 9.91533L10.3587 11.6373C10.2287 11.7673 10.058 11.8333 9.88733 11.8333ZM6.584 11.6347C6.84467 11.374 6.84467 10.9527 6.584 10.692L4.862 8.97C4.736 8.844 4.66667 8.67667 4.66667 8.498C4.66667 8.31933 4.736 8.15267 4.862 8.02667L6.584 6.30467C6.84467 6.044 6.84467 5.62267 6.584 5.362C6.32333 5.10133 5.902 5.10133 5.64133 5.362L3.91933 7.084C3.54133 7.462 3.33333 7.964 3.33333 8.49867C3.33333 9.03333 3.54133 9.53533 3.91933 9.91333L5.64133 11.6353C5.77133 11.7653 5.942 11.8307 6.11267 11.8307C6.28333 11.8307 6.454 11.7647 6.584 11.6347ZM16 13.1667V3.83333C16 1.99533 14.5047 0.5 12.6667 0.5H3.33333C1.49533 0.5 0 1.99533 0 3.83333V13.1667C0 15.0047 1.49533 16.5 3.33333 16.5H12.6667C14.5047 16.5 16 15.0047 16 13.1667ZM12.6667 1.83333C13.7693 1.83333 14.6667 2.73067 14.6667 3.83333V13.1667C14.6667 14.2693 13.7693 15.1667 12.6667 15.1667H3.33333C2.23067 15.1667 1.33333 14.2693 1.33333 13.1667V3.83333C1.33333 2.73067 2.23067 1.83333 3.33333 1.83333H12.6667Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_162">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CodeIcon;
