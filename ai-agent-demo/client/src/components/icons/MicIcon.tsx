const MicIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="20"
      viewBox="0 0 15 20"
      fill="none"
    >
      <path
        d="M13.2344 8.77051C13.4681 8.77063 13.6921 8.85863 13.8574 9.01465C14.0228 9.1708 14.1161 9.38269 14.1162 9.60352V10.001C14.1162 11.6246 13.4886 13.1923 12.3516 14.4102C11.2145 15.628 9.64612 16.412 7.94043 16.6152V18.333H9.70508C9.93896 18.3331 10.1637 18.4209 10.3291 18.5771C10.4945 18.7334 10.5869 18.9461 10.5869 19.167C10.5868 19.3877 10.4943 19.5997 10.3291 19.7559C10.1637 19.9121 9.93896 19.9999 9.70508 20H4.41113C4.17714 20 3.95257 19.9121 3.78711 19.7559C3.62177 19.5997 3.52943 19.3878 3.5293 19.167C3.5293 18.946 3.62165 18.7334 3.78711 18.5771C3.95257 18.4209 4.17715 18.333 4.41113 18.333H6.17578V16.6152C2.69384 16.2053 0.000185525 13.4005 0 10.001V9.60352C0.000133381 9.38273 0.0925314 9.17079 0.257812 9.01465C0.423236 8.85841 0.647896 8.77055 0.881836 8.77051C1.1158 8.77051 1.34041 8.85842 1.50586 9.01465C1.6712 9.1708 1.76452 9.38269 1.76465 9.60352V10.001C1.76475 11.3268 2.3218 12.5986 3.31445 13.5361C4.30713 14.4737 5.65376 14.9999 7.05762 15C8.46144 15 9.80806 14.4735 10.8008 13.5361C11.7934 12.5986 12.3515 11.3268 12.3516 10.001V9.60352C12.3517 9.38269 12.445 9.1708 12.6104 9.01465C12.7758 8.85852 13.0005 8.77051 13.2344 8.77051ZM7.05859 0C7.99455 0 8.89286 0.350552 9.55469 0.975586C10.2165 1.60065 10.5879 2.44903 10.5879 3.33301V9.99902C10.5879 10.8828 10.2163 11.7304 9.55469 12.3555C8.89285 12.9805 7.99457 13.332 7.05859 13.332C6.12273 13.332 5.22524 12.9805 4.56348 12.3555C3.90175 11.7304 3.53029 10.8829 3.53027 9.99902V3.33301C3.53027 2.44903 3.90164 1.60065 4.56348 0.975586C5.22522 0.350727 6.12284 8.08176e-05 7.05859 0Z"
        fill="white"
      />
    </svg>
  );
};

export default MicIcon;
