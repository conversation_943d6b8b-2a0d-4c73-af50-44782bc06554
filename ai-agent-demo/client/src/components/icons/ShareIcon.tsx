import React from "react";

const ShareIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    {...props}
  >
    <path
      d="M12.8886 10.278C12.3845 10.2784 11.8882 10.4015 11.4423 10.6365C10.9964 10.8716 10.6144 11.2117 10.3293 11.6273L5.98993 9.668C6.29814 8.92376 6.29934 8.08779 5.99327 7.34267L10.3266 5.37333C10.7492 5.9845 11.3768 6.42398 12.0957 6.61203C12.8145 6.80009 13.5769 6.72427 14.2447 6.39833C14.9124 6.0724 15.4412 5.51793 15.7352 4.83551C16.0291 4.15309 16.0687 3.38792 15.8468 2.67878C15.625 1.96965 15.1563 1.36353 14.5258 0.970371C13.8953 0.57721 13.1447 0.423049 12.4103 0.535852C11.6759 0.648654 11.0062 1.02095 10.5228 1.5852C10.0393 2.14946 9.77411 2.8683 9.77527 3.61133C9.77809 3.7872 9.79594 3.9625 9.8286 4.13533L5.22194 6.22867C4.7795 5.81415 4.22561 5.53789 3.62832 5.43384C3.03103 5.32979 2.41635 5.40248 1.85981 5.64297C1.30326 5.88347 0.829085 6.28129 0.495532 6.78758C0.161979 7.29386 -0.0164216 7.88655 -0.0177537 8.49283C-0.0190858 9.09912 0.156709 9.69258 0.488034 10.2003C0.819358 10.7081 1.29178 11.108 1.84727 11.3509C2.40275 11.5939 3.0171 11.6692 3.61484 11.5678C4.21258 11.4664 4.76768 11.1926 5.21194 10.78L9.8306 12.8653C9.79852 13.038 9.7809 13.2131 9.77794 13.3887C9.7778 14.0041 9.96018 14.6057 10.302 15.1175C10.6438 15.6292 11.1298 16.0281 11.6983 16.2637C12.2669 16.4993 12.8925 16.5609 13.4961 16.4409C14.0997 16.3209 14.6541 16.0245 15.0893 15.5894C15.5245 15.1542 15.8208 14.5998 15.9409 13.9962C16.0609 13.3926 15.9992 12.7669 15.7636 12.1984C15.5281 11.6298 15.1292 11.1439 14.6174 10.8021C14.1057 10.4602 13.504 10.2779 12.8886 10.278ZM12.8886 1.83333C13.2403 1.8332 13.5841 1.93737 13.8766 2.13266C14.1691 2.32795 14.3971 2.60559 14.5317 2.93046C14.6664 3.25534 14.7017 3.61285 14.6332 3.9578C14.5647 4.30274 14.3954 4.61961 14.1467 4.86833C13.8981 5.11706 13.5813 5.28646 13.2364 5.35512C12.8915 5.42378 12.5339 5.38861 12.209 5.25406C11.8841 5.1195 11.6064 4.89161 11.411 4.59921C11.2156 4.3068 11.1113 3.96302 11.1113 3.61133C11.1116 3.14 11.299 2.68807 11.6322 2.35472C11.9654 2.02138 12.4173 1.83386 12.8886 1.83333ZM3.11127 10.278C2.75958 10.2781 2.41576 10.174 2.12328 9.97868C1.8308 9.78339 1.60281 9.50575 1.46814 9.18087C1.33346 8.856 1.29815 8.49848 1.36668 8.15354C1.43521 7.8086 1.6045 7.49173 1.85313 7.243C2.10176 6.99428 2.41857 6.82487 2.76349 6.75621C3.1084 6.68756 3.46593 6.72273 3.79086 6.85728C4.11578 6.99183 4.39351 7.21972 4.58891 7.51213C4.78431 7.80453 4.8886 8.14832 4.8886 8.5C4.88807 8.97128 4.70067 9.42311 4.36749 9.75642C4.03431 10.0897 3.58255 10.2773 3.11127 10.278ZM12.8886 15.1667C12.5369 15.1667 12.1932 15.0624 11.9008 14.867C11.6084 14.6717 11.3805 14.394 11.2459 14.0691C11.1114 13.7442 11.0762 13.3867 11.1448 13.0418C11.2134 12.6969 11.3827 12.3801 11.6314 12.1314C11.88 11.8828 12.1968 11.7134 12.5417 11.6448C12.8866 11.5762 13.2441 11.6114 13.569 11.746C13.8939 11.8806 14.1716 12.1085 14.367 12.4009C14.5623 12.6933 14.6666 13.037 14.6666 13.3887C14.6662 13.8601 14.4788 14.3122 14.1454 14.6455C13.8121 14.9789 13.36 15.1663 12.8886 15.1667Z"
      fill="white"
    />
  </svg>
);

export default ShareIcon;
