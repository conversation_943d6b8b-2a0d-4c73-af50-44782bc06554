# Docker Environment Configuration
# Copy this file to ai-agent-demo/.env and fill in your actual values

# Google API Key for Gemini
GOOGLE_API_KEY=your_google_api_key_here

# Server Port (default: 8000)
PORT=8000

# Optional: OpenWeather API Key
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Optional: Zapier Webhook URL
ZAPIER_WEBHOOK_URL=your_zapier_webhook_url_here

# VideoSDK Token (for client)
VITE_VIDEOSDK_TOKEN=your_videosdk_token_here

# API URL for client (will be the Docker container)
VITE_API_URL=http://localhost:8000
