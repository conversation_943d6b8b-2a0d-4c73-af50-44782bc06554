# Multi-stage build for AI Agent Demo
# Stage 1: Build the React frontend
FROM node:18-alpine AS frontend-builder

WORKDIR /app/client

# Copy package files
COPY ai-agent-demo/client/package*.json ./
COPY ai-agent-demo/client/bun.lockb ./

# Install dependencies
RUN npm ci --only=production

# Copy client source code
COPY ai-agent-demo/client/ ./

# Build the frontend
RUN npm run build

# Stage 2: Python backend with built frontend
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy Python requirements and install dependencies
COPY ai-agent-demo/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy Python backend code
COPY ai-agent-demo/*.py ./
COPY ai-agent-demo/.env.example ./.env.example

# Copy built frontend from previous stage
COPY --from=frontend-builder /app/client/dist ./static

# Create a simple static file server for the frontend
RUN echo 'from fastapi.staticfiles import StaticFiles' > static_server.py && \
    echo 'import os' >> static_server.py && \
    echo 'from fastapi import FastAPI' >> static_server.py && \
    echo 'from fastapi.responses import FileResponse' >> static_server.py && \
    echo '' >> static_server.py && \
    echo 'def mount_static_files(app: FastAPI):' >> static_server.py && \
    echo '    if os.path.exists("./static"):' >> static_server.py && \
    echo '        app.mount("/static", StaticFiles(directory="static"), name="static")' >> static_server.py && \
    echo '        @app.get("/")' >> static_server.py && \
    echo '        async def read_index():' >> static_server.py && \
    echo '            return FileResponse("static/index.html")' >> static_server.py

# Expose port
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app
ENV PORT=8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["python", "server.py"]
